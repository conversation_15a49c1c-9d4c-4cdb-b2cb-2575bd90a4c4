import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/utils/constants/colors.dart';
import 'package:platix/view/screens/dentist/edr_patient_details_screen.dart';

class EdrPatientListScreen extends StatelessWidget {
  const EdrPatientListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Electronic Dental Records'),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Search Patient',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const TextField(
                decoration: InputDecoration(
                  hintText: 'Search Patient',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 20),
              const Text(
                'Most Recent Patients',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              DataTable(
                showCheckboxColumn: false,
                columnSpacing: 20,
                horizontalMargin: 10,
                headingRowColor: MaterialStateProperty.all(AppColors.primary),
                headingTextStyle: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                columns: const [
                  DataColumn(label: Text('S.No')),
                  DataColumn(label: Text('Patient Name')),
                  DataColumn(label: Text('Age/Gender')),
                  DataColumn(label: Align(alignment: Alignment.centerRight, child: Text('Date'))),
                ],
                rows: [
                  DataRow(
                    color: MaterialStateProperty.all(AppColors.primary2),
                    cells: const [
                      DataCell(Text('1')),
                      DataCell(Text('Matthew Thomas')),
                      DataCell(Text('49Yrs/Male')),
                      DataCell(Align(alignment: Alignment.centerRight, child: Text('30/10/2020'))),
                    ],
                    onSelectChanged: (selected) {
                      if (selected != null && selected) {
                        Get.to(() => const EdrPatientDetailsScreen());
                      }
                    },
                  ),
                  DataRow(
                    color: MaterialStateProperty.all(Colors.white),
                    cells: const [
                      DataCell(Text('2')),
                      DataCell(Text('John Davis')),
                      DataCell(Text('89Yrs/Male')),
                      DataCell(Align(alignment: Alignment.centerRight, child: Text('16/10/2021'))),
                    ],
                    onSelectChanged: (selected) {
                      if (selected != null && selected) {
                        Get.to(() => const EdrPatientDetailsScreen());
                      }
                    },
                  ),
                  DataRow(
                    color: MaterialStateProperty.all(AppColors.primary2),
                    cells: const [
                      DataCell(Text('3')),
                      DataCell(Text('Elizabeth Jones')),
                      DataCell(Text('27Yrs/Female')),
                      DataCell(Align(alignment: Alignment.centerRight, child: Text('10/01/2024'))),
                    ],
                    onSelectChanged: (selected) {
                      if (selected != null && selected) {
                        Get.to(() => const EdrPatientDetailsScreen());
                      }
                    },
                  ),
                  DataRow(
                    color: MaterialStateProperty.all(Colors.white),
                    cells: const [
                      DataCell(Text('4')),
                      DataCell(Text('Ryan Young')),
                      DataCell(Text('48Yrs/Male')),
                      DataCell(Align(alignment: Alignment.centerRight, child: Text('09/01/2022'))),
                    ],
                    onSelectChanged: (selected) {
                      if (selected != null && selected) {
                        Get.to(() => const EdrPatientDetailsScreen());
                      }
                    },
                  ),
                  DataRow(
                    color: MaterialStateProperty.all(AppColors.primary2),
                    cells: const [
                      DataCell(Text('5')),
                      DataCell(Text('Anthony Moore')),
                      DataCell(Text('38Yrs/Female')),
                      DataCell(Align(alignment: Alignment.centerRight, child: Text('30/01/2025'))),
                    ],
                    onSelectChanged: (selected) {
                      if (selected != null && selected) {
                        Get.to(() => const EdrPatientDetailsScreen());
                      }
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
