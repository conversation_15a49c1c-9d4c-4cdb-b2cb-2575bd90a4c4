import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/widgets/custom_elevated_button.dart';
import 'package:platix/view/widgets/custom_outlined_button.dart'; // Import the new custom outlined button
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';

class AppointmentsScreen extends StatefulWidget {
  const AppointmentsScreen({super.key});

  @override
  State<AppointmentsScreen> createState() => _AppointmentsScreenState();
}

class _AppointmentsScreenState extends State<AppointmentsScreen> {
  bool _isMonthView = false;
  DateTime _selectedDay = DateTime.now();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Appointments'),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    'Book Appointment',
                    style: CustomTextStyles.b4_1,
                  ),
                  const SizedBox(width: 8),
                  GestureDetector(
                    onTap: () {
                      Get.toNamed(AppRoutes.createAppointmentScreen);
                    },
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.add,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: CustomElevatedButton(
                      onPressed: () {
                        setState(() {
                          _isMonthView = false;
                        });
                      },
                      text: 'Day',
                      height: 40, // Set a consistent height
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: CustomOutlinedButton( // Use CustomOutlinedButton
                      onPressed: () {
                        setState(() {
                          _isMonthView = true;
                        });
                      },
                      text: 'Month',
                      height: 40, // Set a consistent height
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              if (_isMonthView)
                _buildMonthView()
              else
                _buildDayView(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDayView() {
    final now = DateTime.now();
    final daysInMonth = DateTime(now.year, now.month + 1, 0).day;
    final List<Widget> dateCards = [];

    for (int i = 1; i <= daysInMonth; i++) {
      final date = DateTime(now.year, now.month, i);
      final isSelected = date.day == _selectedDay.day && date.month == _selectedDay.month && date.year == _selectedDay.year;
      dateCards.add(
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 5.0),
          child: GestureDetector(
            onTap: () {
              setState(() {
                _selectedDay = date;
              });
            },
            child: _buildDateCard(
              DateFormat('dd').format(date),
              DateFormat('EEE').format(date),
              isSelected,
            ),
          ),
        ),
      );
    }

    return Column(
      children: [
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start, // Changed to start to allow natural scrolling
            children: dateCards,
          ),
        ),
        const SizedBox(height: 20),
        _buildAppointmentSlot('9:00 AM', null),
        _buildAppointmentSlot('9:30 AM', null),
        _buildAppointmentSlot('10:00 AM', 'VISHALA, RCT'),
        _buildAppointmentSlot('10:30 AM', null),
        _buildAppointmentSlot('11:00 AM', null),
        _buildAppointmentSlot('11:30 AM', 'SURESH, EXTRN'),
        _buildAppointmentSlot('12:00 PM', null),
        _buildAppointmentSlot('12:30 PM', null),
        _buildAppointmentSlot('1:00 PM', null),
        _buildAppointmentSlot('1:30 PM', null),
        _buildAppointmentSlot('2:00 PM', null),
      ],
    );
  }

  Widget _buildMonthView() {
    return Column(
      children: [
        TableCalendar(
          firstDay: DateTime.utc(2010, 10, 16),
          lastDay: DateTime.utc(2030, 3, 14),
          focusedDay: _selectedDay, // Use _selectedDay as focusedDay
          calendarFormat: CalendarFormat.month,
          headerStyle: const HeaderStyle(
            formatButtonVisible: false,
            titleCentered: true,
          ),
          selectedDayPredicate: (day) {
            // Use `selectedDayPredicate` to determine which day is currently selected.
            // If this returns true, then `day` will be marked as selected.
            return isSameDay(_selectedDay, day);
          },
          onDaySelected: (selectedDay, focusedDay) {
            setState(() {
              _selectedDay = selectedDay;
              _isMonthView = false; // Switch to Day view
            });
          },
          onPageChanged: (focusedDay) {
            _selectedDay = focusedDay; // Update focused day when page changes
          },
        ),
        const SizedBox(height: 20),
        _buildAppointmentSlot('11:30 AM', 'SURESH, EXTRN'),
        _buildAppointmentSlot('12:00 PM', null),
        _buildAppointmentSlot('12:30 PM', null),
        _buildAppointmentSlot('1:00 PM', null),
        _buildAppointmentSlot('1:30 PM', null),
        _buildAppointmentSlot('2:00 PM', null),
      ],
    );
  }

  Widget _buildDateCard(String date, String day, bool isSelected) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
      decoration: BoxDecoration(
        color: isSelected ? AppColors.primary : Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: AppColors.primary),
      ),
      child: Column(
        children: [
          Text(
            date,
            style: TextStyle(
              color: isSelected ? Colors.white : AppColors.primary,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            day,
            style: TextStyle(
              color: isSelected ? Colors.white : AppColors.primary,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAppointmentSlot(String time, String? details) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 5),
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: details != null ? AppColors.primary2 : Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: AppColors.primary),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            time,
            style: const TextStyle(fontSize: 16),
          ),
          if (details != null)
            Text(
              details,
              style: const TextStyle(fontSize: 16, color: AppColors.primary),
            ),
        ],
      ),
    );
  }
}
