import 'dart:io';
import 'package:excel/excel.dart' as excel;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:platix/utils/constants/colors.dart';
import 'package:platix/utils/constants/icon_constants.dart';
import 'package:platix/utils/constants/sizes.dart';
import 'package:platix/view/widgets/custom_elevated_button.dart';
import 'package:platix/view/widgets/custom_image_view.dart';
import 'package:share_plus/share_plus.dart';

import '../../../theme/app_decoration.dart';
import '../../../theme/custom_text_style.dart';

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  DateTime? fromDate;
  DateTime? toDate;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Reports'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.white,
      ),
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: AppSizes.defaultSpace),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
              child: Row(
                mainAxisAlignment: MediaQuery.of(context).size.width > 600
                    ? MainAxisAlignment.center
                    : MainAxisAlignment.start,
                children: [
                  _buildDateField(fromDate, (date) {
                    setState(() {
                      fromDate = date;
                    });
                  }, "From"),
                  const SizedBox(width: AppSizes.spaceSmall),
                  _buildDateField(toDate, (date) {
                    setState(() {
                      toDate = date;
                    });
                  }, "To"),
                  MediaQuery.of(context).size.width > 600
                      ? const SizedBox(
                          width: AppSizes.defaultSpace,
                        )
                      : const Spacer(),
                  CustomElevatedButton(
                    onPressed: () async {
                      if (fromDate != null && toDate != null) {
                        await generateAndDownloadExcel();
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content:
                                Text('Please select both From and To dates.'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    },
                    buttonStyle: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        borderRadius:
                            BorderRadius.circular(AppSizes.borderRadiusSm),
                      ),
                      shadowColor: AppColors.black.withOpacity(0.25),
                    ),
                    leftIcon: Padding(
                      padding: const EdgeInsets.only(bottom: 2.0),
                      child: CustomImageView(
                        imagePath: Platform.isAndroid
                            ? AppIcons.download
                            : AppIcons.download,
                      ),
                    ),
                    text: Platform.isAndroid ? 'Download' : 'Share',
                    buttonTextStyle:
                        CustomTextStyles.b6_1.copyWith(color: AppColors.white),
                    height: 32,
                    width: MediaQuery.of(context).size.width > 600
                        ? 119
                        : Get.size.width * 0.28,
                  ),
                ],
              ),
            ),
            const SizedBox(height: AppSizes.defaultSpace),
            Container(
              color: Colors.white,
              child: TabBar(
                controller: _tabController,
                isScrollable: false,
                unselectedLabelColor: AppColors.black,
                indicatorColor: AppColors.primary,
                labelColor: AppColors.black,
                tabs: const [
                  Tab(text: 'PMS Revenue'),
                  Tab(text: 'Patient Reports'),
                  Tab(text: 'Order Reports'),
                ],
              ),
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: const [
                  SizedBox.shrink(),
                  SizedBox.shrink(),
                  SizedBox.shrink(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateField(
      DateTime? date, Function(DateTime) onDateSelected, String hintText) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            if (hintText == "To" && fromDate != null) {
              _selectDate(context, onDateSelected, firstDate: fromDate);
            } else {
              _selectDate(context, onDateSelected);
            }
          },
          child: Container(
            height: 32,
            width: Get.size.width * 0.3,
            decoration: BoxDecoration(
              boxShadow: AppDecoration.shadow1_3,
              color: Colors.white,
              borderRadius: BorderRadiusStyle.radius8,
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppSizes.sm2),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    date != null
                        ? DateFormat('dd-MM-yyyy').format(date)
                        : hintText,
                    style: date != null
                        ? CustomTextStyles.b6.copyWith(
                            color: AppColors.black, fontWeight: FontWeight.w500)
                        : CustomTextStyles.b6_1
                            .copyWith(color: AppColors.primary),
                  ),
                  CustomImageView(
                    imagePath: AppIcons.calender,
                    color: AppColors.primary,
                    height: 16,
                    width: 16,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate(
      BuildContext context, Function(DateTime) onDateSelected,
      {DateTime? firstDate}) async {
    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: firstDate ?? DateTime.now(),
      firstDate: firstDate ?? DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (pickedDate != null) {
      onDateSelected(pickedDate);
    }
  }

  String _getReportTypeForIndex(int index) {
    switch (index) {
      case 0:
        return 'PMS Revenue';
      case 1:
        return 'Patient Reports';
      case 2:
        return 'Order Reports';
      default:
        return '';
    }
  }

  List<String> _getHeaders() {
    final reportType = _getReportTypeForIndex(_tabController.index);
    switch (reportType) {
      case 'PMS Revenue':
        return [
          "Sno",
          "Date",
          "Patient ID",
          "Patient first name",
          "Patient last name",
          "Patient age",
          "Patient gender",
          "Patient address",
          "Patient mobile number",
          "Service name",
          "Doctor name",
          "Service price",
          "Paid amount",
          "Discount",
          "Balance",
          "Payment source"
        ];
      case 'Patient Reports':
        return [
          "Sno",
          "Date",
          "Patient ID",
          "Patient first name",
          "Patient last name",
          "Patient age",
          "Patient gender",
          "Patient date of birth",
          "Patient mobile number",
          "Patient email",
          "Address",
          "State",
          "Country"
        ];
      case 'Order Reports':
        return [
          "S.no",
          "Order ID",
          "Order date",
          "From",
          "To",
          "Patient first name",
          "Patient last name",
          "Service name",
          "Invoice amount",
          "Paid amount",
          "Balance",
          "Mode of payment",
          "Order status"
        ];
      default:
        return [];
    }
  }

  List<Map<String, dynamic>> _getDummyData() {
    final reportType = _getReportTypeForIndex(_tabController.index);
    switch (reportType) {
      case 'PMS Revenue':
        return [
          {
            "Sno": 1,
            "Date": "2024-07-23",
            "Patient ID": "P001",
            "Patient first name": "John",
            "Patient last name": "Doe",
            "Patient age": 34,
            "Patient gender": "Male",
            "Patient address": "123 Main St",
            "Patient mobile number": "**********",
            "Service name": "Cleaning",
            "Doctor name": "Dr. Smith",
            "Service price": 100.0,
            "Paid amount": 100.0,
            "Discount": 0.0,
            "Balance": 0.0,
            "Payment source": "Cash"
          },
        ];
      case 'Patient Reports':
        return [
          {
            "Sno": 1,
            "Date": "2024-07-23",
            "Patient ID": "P001",
            "Patient first name": "John",
            "Patient last name": "Doe",
            "Patient age": 34,
            "Patient gender": "Male",
            "Patient date of birth": "1990-01-15",
            "Patient mobile number": "**********",
            "Patient email": "<EMAIL>",
            "Address": "123 Main St",
            "State": "California",
            "Country": "USA"
          },
        ];
      case 'Order Reports':
        return [
          {
            "S.no": 1,
            "Order ID": "O001",
            "Order date": "2024-07-23",
            "From": "Lab A",
            "To": "Lab B",
            "Patient first name": "John",
            "Patient last name": "Doe",
            "Service name": "Blood Test",
            "Invoice amount": 150.0,
            "Paid amount": 150.0,
            "Balance": 0.0,
            "Mode of payment": "Card",
            "Order status": "Completed"
          },
        ];
      default:
        return [];
    }
  }

  Future<void> generateAndDownloadExcel() async {
    final reportType = _getReportTypeForIndex(_tabController.index);

    var xlsx = excel.Excel.createExcel();
    String sheetName = reportType.replaceAll(' ', '_');
    xlsx.rename('Sheet1', sheetName);
    excel.Sheet sheet = xlsx[sheetName];

    List<String> headers = _getHeaders();
    for (var i = 0; i < headers.length; i++) {
      sheet
          .cell(excel.CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .value = excel.TextCellValue(headers[i]);
    }

    List<Map<String, dynamic>> dummyData = _getDummyData();

    for (var i = 0; i < dummyData.length; i++) {
      var rowData = dummyData[i];
      for (var j = 0; j < headers.length; j++) {
        final key = headers[j];
        final value = rowData[key];
        var cellValue;
        if (value is int) {
          cellValue = excel.IntCellValue(value);
        } else if (value is double) {
          cellValue = excel.DoubleCellValue(value);
        } else {
          cellValue = excel.TextCellValue(value.toString());
        }
        sheet
            .cell(
                excel.CellIndex.indexByColumnRow(columnIndex: j, rowIndex: i + 1))
            .value = cellValue;
      }
    }

    final fileBytes = xlsx.encode();
    if (fileBytes == null) return;

    try {
      final reportName = reportType.replaceAll(' ', '_');
      if (Platform.isAndroid) {
        final directory = Directory("/storage/emulated/0/Download");
        if (!await directory.exists()) {
          await directory.create(recursive: true);
        }
        final filePath =
            "${directory.path}/${reportName}_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.xlsx";
        final file = File(filePath);
        await file.writeAsBytes(fileBytes);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Report downloaded to $filePath'),
              backgroundColor: Colors.green,
            ),
          );
          OpenFile.open(filePath);
        }
      } else if (Platform.isIOS) {
        final directory = await getApplicationDocumentsDirectory();
        final filePath = "${directory.path}/${reportName}.xlsx";
        final file = File(filePath);
        await file.writeAsBytes(fileBytes);
        await Share.shareXFiles([XFile(filePath)],
            text: 'Here is your report.');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save report: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
