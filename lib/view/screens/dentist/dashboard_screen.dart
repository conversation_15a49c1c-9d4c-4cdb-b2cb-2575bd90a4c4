import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/api/data_store.dart';
import 'package:platix/controllers/cart/cart_controller.dart';
import 'package:platix/controllers/dentist_controllers/dentist_home_controller.dart';
import 'package:platix/controllers/dentist_controllers/store_controller.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/screens/signinOption_screen.dart';
import 'package:badges/badges.dart' as badges;
import 'package:platix/view/screens/cart_screen.dart';
import 'package:intl/intl.dart'; // Added import
import '../../../controllers/dentist_controllers/dentist_search_controller.dart';
import '../../../data/models/dentist/dentist_search_model.dart';
import '../../../data/models/user_model.dart';
import '../../../utils/web_responsive_utils.dart';
import '../notification_screen.dart';
import 'dentist_service_details_screen.dart';
import 'item_details_screen.dart';
import '../store_homescreen.dart';
import '../../widgets/custom_search_dropdown.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:platix/utils/constants/colors.dart';
import '../../widgets/dentist_drawer.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  HomeController homeController = Get.put(HomeController());
  final DentistSearchController searchControllerGet = Get.put(DentistSearchController());
  final StoreController storeController = Get.put(StoreController());

  List<Organization1> searchResults = [];
  bool isSearching = false;
  bool isVerifyingEmail = false;
  final UserRecord? userRecord = getData.read('userRecord') == null ? null : UserRecord.fromJson(getData.read('userRecord'));
  String? token = getData.read('token');

  // Date variables for Revenue and Cost sections
  DateTime? fromDateRevenue;
  DateTime? toDateRevenue;
  DateTime? fromDateCost;
  DateTime? toDateCost;
  DateTime? fromDateServices; // Added for Services section
  DateTime? toDateServices;   // Added for Services section

  @override
  void initState() {
    super.initState();
    // Initialize dates to current date
    fromDateRevenue = DateTime.now();
    toDateRevenue = DateTime.now();
    fromDateCost = DateTime.now();
    toDateCost = DateTime.now();
    fromDateServices = DateTime.now(); // Initialize for Services
    toDateServices = DateTime.now();   // Initialize for Services
  }

  // Function to show date picker
  Future<void> _selectDate(BuildContext context, Function(DateTime) onDateSelected, {DateTime? initialDate, DateTime? firstDate, DateTime? lastDate}) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: initialDate ?? DateTime.now(),
      firstDate: firstDate ?? DateTime(2000),
      lastDate: lastDate ?? DateTime(2100),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: Colors.white,
              onSurface: AppColors.black,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppColors.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      onDateSelected(pickedDate);
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CartController>(
      init: CartController(),
      builder: (cartController) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Dashboard'),
          ),
          body: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Padding(
                //   padding: const EdgeInsets.all(16.0),
                //   child: Container(
                //     decoration: BoxDecoration(
                //       boxShadow: AppDecoration.shadow1_3,
                //       borderRadius: BorderRadiusStyle.radius8,
                //     ),
                //     child: CustomSearchField<String>(
                //       fetchItems: (query) => [],
                //       hintText: "Search",
                //       itemAsString: (item) => item,
                //       onSelected: (item) {
                //         log("Selected Item: $item");
                //       },
                //       onChanged: (query) async {
                //         if (query.isNotEmpty) {
                //           setState(() {
                //             isSearching = true;
                //           });
                //
                //           try {
                //             final List<Organization1> results = await searchControllerGet.organizationSearch(query);
                //
                //             setState(() {
                //               searchResults = results.map((org) => Organization1(
                //                 id: org.id,
                //                 name: org.name,
                //                 file1: org.file1,
                //                 address: org.address,
                //                 mobile: org.mobile,
                //                 email: org.email,
                //                 description: org.description,
                //                 organizationType: org.organizationType,
                //                 organizationTypeId: org.organizationTypeId,
                //               )).toList();
                //             });
                //           } catch (e) {
                //             log("Search API Error: $e");
                //             setState(() {
                //               searchResults = [];
                //             });
                //           }
                //         } else {
                //           setState(() {
                //             isSearching = false;
                //             searchResults.clear();
                //           });
                //         }
                //       },
                //     ),
                //   ),
                // ),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    'DASHBOARDS',
                    style: CustomTextStyles.b4_1.copyWith(color: AppColors.primary),
                  ),
                ),
                const SizedBox(height: AppSizes.md),
                // REVENUE Section
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'REVENUE',
                        style: CustomTextStyles.b4_1,
                      ),
                      Row(
                        children: [
                          GestureDetector( // Changed to GestureDetector
                            onTap: () => _selectDate(context, (date) {
                              setState(() {
                                fromDateRevenue = date;
                              });
                            }, initialDate: fromDateRevenue),
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: AppSizes.sm, vertical: AppSizes.xs),
                              decoration: BoxDecoration(
                                border: Border.all(color: AppColors.primary),
                                borderRadius: BorderRadius.circular(AppSizes.xs),
                              ),
                              child: Row(
                                children: [
                                  const Icon(Icons.calendar_today, size: 16, color: AppColors.primary),
                                  const SizedBox(width: AppSizes.xs),
                                  Text(
                                    fromDateRevenue != null ? DateFormat('dd MMM yyyy').format(fromDateRevenue!) : 'Select Date',
                                    style: CustomTextStyles.b6_3,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(width: AppSizes.sm),
                          GestureDetector( // Changed to GestureDetector
                            onTap: () => _selectDate(context, (date) {
                              setState(() {
                                toDateRevenue = date;
                              });
                            }, initialDate: toDateRevenue),
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: AppSizes.sm, vertical: AppSizes.xs),
                              decoration: BoxDecoration(
                                border: Border.all(color: AppColors.primary),
                                borderRadius: BorderRadius.circular(AppSizes.xs),
                              ),
                              child: Row(
                                children: [
                                  const Icon(Icons.calendar_today, size: 16, color: AppColors.primary),
                                  const SizedBox(width: AppSizes.xs),
                                  Text(
                                    toDateRevenue != null ? DateFormat('dd MMM yyyy').format(toDateRevenue!) : 'Select Date',
                                    style: CustomTextStyles.b6_3,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppSizes.sm),
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                  padding: const EdgeInsets.all(AppSizes.md),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: AppDecoration.shadow1_3,
                    borderRadius: BorderRadiusStyle.radius8,
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          Row(
                            children: [
                              Container(
                                width: 10,
                                height: 10,
                                decoration: BoxDecoration(
                                  color: AppColors.primary,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: AppSizes.xs),
                              Text('Label 1', style: CustomTextStyles.b6_3),
                            ],
                          ),
                          Row(
                            children: [
                              Container(
                                width: 10,
                                height: 10,
                                decoration: BoxDecoration(
                                  color: AppColors.primary5,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: AppSizes.xs),
                              Text('Label 2', style: CustomTextStyles.b6_3),
                            ],
                          ),
                          Row(
                            children: [
                              Container(
                                width: 10,
                                height: 10,
                                decoration: BoxDecoration(
                                  color: AppColors.primary3,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: AppSizes.xs),
                              Text('Label 3', style: CustomTextStyles.b6_3),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: AppSizes.md),
                      SizedBox(
                        height: 220,
                        child: PieChart(
                          PieChartData(
                            sections: [
                              PieChartSectionData(
                                color: AppColors.primary,
                                value: 40,
                                title: '',
                                radius: 70,
                              ),
                              PieChartSectionData(
                                color: AppColors.primary5,
                                value: 30,
                                title: '',
                                radius: 70,
                              ),
                              PieChartSectionData(
                                color: AppColors.primary4,
                                value: 30,
                                title: '',
                                radius: 70,
                              ),
                            ],
                            sectionsSpace: 0,
                            centerSpaceRadius: 35,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppSizes.spaceBtwItems),
                // SERVICES Section
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'SERVICES',
                        style: CustomTextStyles.b4_1,
                      ),
                      Row(
                        children: [
                          GestureDetector( // Changed to GestureDetector
                            onTap: () => _selectDate(context, (date) {
                              setState(() {
                                fromDateServices = date;
                              });
                            }, initialDate: fromDateServices),
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: AppSizes.sm, vertical: AppSizes.xs),
                              decoration: BoxDecoration(
                                border: Border.all(color: AppColors.primary),
                                borderRadius: BorderRadius.circular(AppSizes.xs),
                              ),
                              child: Row(
                                children: [
                                  const Icon(Icons.calendar_today, size: 16, color: AppColors.primary),
                                  const SizedBox(width: AppSizes.xs),
                                  Text(
                                    fromDateServices != null ? DateFormat('dd MMM yyyy').format(fromDateServices!) : 'Select Date',
                                    style: CustomTextStyles.b6_3,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(width: AppSizes.sm),
                          GestureDetector( // Changed to GestureDetector
                            onTap: () => _selectDate(context, (date) {
                              setState(() {
                                toDateServices = date;
                              });
                            }, initialDate: toDateServices),
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: AppSizes.sm, vertical: AppSizes.xs),
                              decoration: BoxDecoration(
                                border: Border.all(color: AppColors.primary),
                                borderRadius: BorderRadius.circular(AppSizes.xs),
                              ),
                              child: Row(
                                children: [
                                  const Icon(Icons.calendar_today, size: 16, color: AppColors.primary),
                                  const SizedBox(width: AppSizes.xs),
                                  Text(
                                    toDateServices != null ? DateFormat('dd MMM yyyy').format(toDateServices!) : 'Select Date',
                                    style: CustomTextStyles.b6_3,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppSizes.sm),
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                  padding: const EdgeInsets.all(AppSizes.md),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: AppDecoration.shadow1_3,
                    borderRadius: BorderRadiusStyle.radius8,
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          Row(
                            children: [
                              Container(
                                width: 10,
                                height: 10,
                                decoration: BoxDecoration(
                                  color: AppColors.primary,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: AppSizes.xs),
                              Text('Label 1', style: CustomTextStyles.b6_3),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: AppSizes.md),
                      SizedBox(
                        height: 180,
                        child: RotatedBox(
                          quarterTurns: 1,
                          child: BarChart(
                            BarChartData(
                              barGroups: [
                                BarChartGroupData(
                                  x: 0,
                                  barRods: [
                                    BarChartRodData(
                                      toY: 8,
                                      color: AppColors.primary5,
                                      width: 10,
                                    ),
                                  ],
                                ),
                                BarChartGroupData(
                                  x: 1,
                                  barRods: [
                                    BarChartRodData(
                                      toY: 10,
                                      color: AppColors.primary5,
                                      width: 10,
                                    ),
                                  ],
                                ),
                                BarChartGroupData(
                                  x: 2,
                                  barRods: [
                                    BarChartRodData(
                                      toY: 14,
                                      color: AppColors.primary5,
                                      width: 10,
                                    ),
                                  ],
                                ),
                                BarChartGroupData(
                                  x: 3,
                                  barRods: [
                                    BarChartRodData(
                                      toY: 15,
                                      color: AppColors.primary5,
                                      width: 10,
                                    ),
                                  ],
                                ),
                              ],
                              titlesData: FlTitlesData(
                                show: true,
                                bottomTitles: AxisTitles(
                                  sideTitles: SideTitles(showTitles: false),
                                ),
                                leftTitles: AxisTitles(
                                  sideTitles: SideTitles(showTitles: false),
                                ),
                                topTitles: AxisTitles(
                                  sideTitles: SideTitles(
                                    showTitles: true,
                                    getTitlesWidget: (value, meta) {
                                      final text = switch (value.toInt()) {
                                        0 => 'Jan',
                                        1 => 'Feb',
                                        2 => 'Mar',
                                        3 => 'Apr',
                                        _ => '',
                                      };
                                      return Text(text, style: CustomTextStyles.b6_3);
                                    },
                                  ),
                                ),
                                rightTitles: AxisTitles(
                                  sideTitles: SideTitles(showTitles: false),
                                ),
                              ),
                              borderData: FlBorderData(
                                show: true,
                                border: Border.all(color: AppColors.lightGrey, width: 1),
                              ),
                              gridData: FlGridData(
                                show: true,
                                drawVerticalLine: true,
                                drawHorizontalLine: true,
                                getDrawingHorizontalLine: (value) => FlLine(
                                  color: AppColors.lightGrey,
                                  strokeWidth: 1,
                                ),
                                getDrawingVerticalLine: (value) => FlLine(
                                  color: AppColors.lightGrey,
                                  strokeWidth: 1,
                                ),
                              ),
                            ),
                            swapAnimationDuration: const Duration(milliseconds: 150), // Optional
                            swapAnimationCurve: Curves.linear, // Optional
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppSizes.spaceBtwItems),
                // PATIENTS Section (New)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'PATIENTS',
                        style: CustomTextStyles.b4_1,
                      ),
                      Row(
                        children: [
                          GestureDetector(
                            onTap: () => _selectDate(context, (date) {
                              setState(() {
                                // Add state variable for fromDatePatients
                              });
                            }, initialDate: DateTime.now()),
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: AppSizes.sm, vertical: AppSizes.xs),
                              decoration: BoxDecoration(
                                border: Border.all(color: AppColors.primary),
                                borderRadius: BorderRadius.circular(AppSizes.xs),
                              ),
                              child: Row(
                                children: [
                                  const Icon(Icons.calendar_today, size: 16, color: AppColors.primary),
                                  const SizedBox(width: AppSizes.xs),
                                  Text(
                                    DateFormat('dd MMM yyyy').format(DateTime.now()), // Use current date for now
                                    style: CustomTextStyles.b6_3,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(width: AppSizes.sm),
                          GestureDetector(
                            onTap: () => _selectDate(context, (date) {
                              setState(() {
                                // Add state variable for toDatePatients
                              });
                            }, initialDate: DateTime.now()),
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: AppSizes.sm, vertical: AppSizes.xs),
                              decoration: BoxDecoration(
                                border: Border.all(color: AppColors.primary),
                                borderRadius: BorderRadius.circular(AppSizes.xs),
                              ),
                              child: Row(
                                children: [
                                  const Icon(Icons.calendar_today, size: 16, color: AppColors.primary),
                                  const SizedBox(width: AppSizes.xs),
                                  Text(
                                    DateFormat('dd MMM yyyy').format(DateTime.now()), // Use current date for now
                                    style: CustomTextStyles.b6_3,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppSizes.sm),
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                  padding: const EdgeInsets.all(AppSizes.md),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    boxShadow: AppDecoration.shadow1_3,
                    borderRadius: BorderRadiusStyle.radius8,
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          Row(
                            children: [
                              Container(
                                width: 10,
                                height: 10,
                                decoration: BoxDecoration(
                                  color: AppColors.primary,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: AppSizes.xs),
                              Text('New Patients', style: CustomTextStyles.b6_3),
                            ],
                          ),
                          Row(
                            children: [
                              Container(
                                width: 10,
                                height: 10,
                                decoration: BoxDecoration(
                                  color: AppColors.primary5,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: AppSizes.xs),
                              Text('Returning Patients', style: CustomTextStyles.b6_3),
                            ],
                          ),
                        ],
                      ),
                      const SizedBox(height: AppSizes.md),
                      SizedBox(
                        height: 220,
                        child: PieChart(
                          PieChartData(
                            sections: [
                              PieChartSectionData(
                                color: AppColors.primary,
                                value: 60,
                                title: '',
                                radius: 70,
                              ),
                              PieChartSectionData(
                                color: AppColors.primary5,
                                value: 40,
                                title: '',
                                radius: 70,
                              ),
                            ],
                            sectionsSpace: 0,
                            centerSpaceRadius: 35,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppSizes.spaceBtwItems),
              ],
            ),
          ),
        );
      },
    );
  }
}
