import 'package:country_state_city_picker/country_state_city_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/widgets/custom_elevated_button.dart';
import 'package:platix/view/widgets/label_text_field.dart';
import 'package:intl/intl.dart'; // Import for DateFormat

class PatientRegistrationScreen extends StatefulWidget {
  const PatientRegistrationScreen({super.key});

  @override
  State<PatientRegistrationScreen> createState() => _PatientRegistrationScreenState();
}

class _PatientRegistrationScreenState extends State<PatientRegistrationScreen> {
  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController mobileNumController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController ageController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  String? _selectedGender;
  DateTime? _selectedDateOfBirth;
  TextEditingController country = TextEditingController();
  TextEditingController state = TextEditingController();
  TextEditingController city = TextEditingController();

  @override
  void dispose() {
    firstNameController.dispose();
    lastNameController.dispose();
    mobileNumController.dispose();
    emailController.dispose();
    ageController.dispose();
    addressController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDateOfBirth ?? DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != _selectedDateOfBirth) {
      setState(() {
        _selectedDateOfBirth = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('New Patient Registration'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'New Patient',
              style: CustomTextStyles.b4_1,
            ),
            const SizedBox(height: 20),
            LabelTextField(
              label: 'First Name',
              hint: 'Enter First Name',
              controller: firstNameController,
            ),
            const SizedBox(height: 15),
            LabelTextField(
              label: 'Last Name',
              hint: 'Enter Last Name',
              controller: lastNameController,
            ),
            const SizedBox(height: 15),
            LabelTextField(
              label: 'Mobile Num',
              hint: 'Enter Mobile Number',
              controller: mobileNumController,
              inputType: TextInputType.phone, // Changed to inputType
            ),
            const SizedBox(height: 15),
            LabelTextField(
              label: 'Email',
              hint: 'Enter Email',
              controller: emailController,
              inputType: TextInputType.emailAddress, // Changed to inputType
            ),
            const SizedBox(height: 15),
            Text(
              'Gender',
              style: CustomTextStyles.b4_1,
            ),
            Row(
              children: [
                Radio<String>(
                  value: 'M',
                  groupValue: _selectedGender,
                  onChanged: (String? value) {
                    setState(() {
                      _selectedGender = value;
                    });
                  },
                ),
                const Text('M'),
                Radio<String>(
                  value: 'F',
                  groupValue: _selectedGender,
                  onChanged: (String? value) {
                    setState(() {
                      _selectedGender = value;
                    });
                  },
                ),
                const Text('F'),
              ],
            ),
            const SizedBox(height: 15),
            Row(
              children: [
                Expanded(
                  child: LabelTextField(
                    label: 'Age',
                    hint: 'Enter Age',
                    controller: ageController,
                    inputType: TextInputType.number, // Changed to inputType
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: GestureDetector(
                    onTap: () => _selectDate(context),
                    child: AbsorbPointer(
                      child: LabelTextField(
                        label: 'DOB.',
                        hint: _selectedDateOfBirth == null
                            ? 'Select Date'
                            : DateFormat('dd/MM/yyyy').format(_selectedDateOfBirth!),
                        controller: TextEditingController(text: _selectedDateOfBirth == null ? '' : DateFormat('dd/MM/yyyy').format(_selectedDateOfBirth!)),
                        suffix: Icon(Icons.calendar_today), // Changed to suffix
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 15),
            LabelTextField(
              label: 'Address',
              hint: 'Enter Address',
              controller: addressController,
              maxLines: 3,
            ),
            const SizedBox(height: 15),
            SelectState(
              onCountryChanged: (value) {
                setState(() {
                  country.text = value;
                });
              },
              onStateChanged: (value) {
                setState(() {
                  state.text = value;
                });
              },
              onCityChanged: (value) {
                setState(() {
                  city.text = value;
                });
              },
            ),
            const SizedBox(height: 30),
            CustomElevatedButton(
              onPressed: () {
                // Handle save logic
                Get.snackbar('Success', 'Patient Registered Successfully!');
              },
              text: 'Save',
            ),
          ],
        ),
      ),
    );
  }
}
