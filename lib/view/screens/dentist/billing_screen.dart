import 'package:flutter/material.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/widgets/custom_elevated_button.dart';
import 'package:platix/view/widgets/custom_dropdown_button.dart';
import 'package:platix/view/widgets/custom_text_form_field.dart';

class BillingScreen extends StatefulWidget {
  const BillingScreen({super.key});

  @override
  State<BillingScreen> createState() => _BillingScreenState();
}

class _BillingScreenState extends State<BillingScreen> {
  String? _selectedPaymentSource;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Patient Billing'),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Text(
                    'Search Patient',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    ' *',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: 'Search Patient',
                prefix: Icon(Icons.search),
              ),
              const SizedBox(height: 20),
              const Text(
                'First Name',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: 'Enter First Name',
              ),
              const SizedBox(height: 20),
              const Text(
                'Last Name',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: 'Enter Last Name',
              ),
              const SizedBox(height: 20),
              const Text(
                'Patient ID',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: 'Enter Patient ID',
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Age',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        const CustomTextFormField(
                          hintText: '10',
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Date Of Birth',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        const CustomTextFormField(
                          hintText: '09 Feb 2021',
                          prefix: Icon(Icons.calendar_today),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              const Text(
                'Gender',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              Row(
                children: [
                  Radio(
                    value: 'Male',
                    groupValue: 'Male',
                    onChanged: (value) {},
                  ),
                  const Text('Male'),
                  Radio(
                    value: 'Female',
                    groupValue: 'Male',
                    onChanged: (value) {},
                  ),
                  const Text('Female'),
                ],
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  const Text(
                    'Search Service',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    ' *',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: 'Search Service Name',
                prefix: Icon(Icons.search),
              ),
              const SizedBox(height: 20),
              const Text(
                'Price',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: 'Enter Price',
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Discount (%)',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        const CustomTextFormField(
                          hintText: '10',
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Discount (Amount)',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        const CustomTextFormField(
                          hintText: '10',
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              const Text(
                'Total Amount',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: 'Enter Total Amount',
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  const Text(
                    'Paid Amount',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    ' *',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: 'Enter Paid Amount',
              ),
              const SizedBox(height: 20),
              const Text(
                'Balance Amount',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const CustomTextFormField(
                hintText: 'Enter Balance Amount',
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  const Text(
                    'Payment Source',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    ' *',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              CustomDropdownButton(
                hintText: 'Select Payment Source',
                items: [
                  DropdownMenuItem(
                    value: 'UPI',
                    child: Row(
                      children: [
                        Icon(Icons.payment),
                        SizedBox(width: 8),
                        Text('UPI'),
                      ],
                    ),
                  ),
                  DropdownMenuItem(
                    value: 'Card',
                    child: Row(
                      children: [
                        Icon(Icons.credit_card),
                        SizedBox(width: 8),
                        Text('Card'),
                      ],
                    ),
                  ),
                  DropdownMenuItem(
                    value: 'Cash',
                    child: Row(
                      children: [
                        Icon(Icons.money),
                        SizedBox(width: 8),
                        Text('Cash'),
                      ],
                    ),
                  ),
                ],
                value: _selectedPaymentSource,
                onChanged: (newValue) {
                  setState(() {
                    _selectedPaymentSource = newValue;
                  });
                },
              ),
              const SizedBox(height: 30),
              CustomElevatedButton(
                onPressed: () {},
                text: 'Save',
              ),
            ],
          ),
        ),
      ),
    );
  }
}
