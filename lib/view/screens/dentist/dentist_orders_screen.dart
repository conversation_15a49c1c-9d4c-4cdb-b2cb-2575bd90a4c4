import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/screens/dentist/create_order_selection_screen.dart';
import 'package:platix/view/screens/dentist/dentist_order_details_screen.dart';
import '../../../controllers/dentist_controllers/dentist_order_controller.dart';
import '../../../controllers/dentist_controllers/dentist_service_controller.dart';
import '../../../data/models/dentist/dentist_order_model.dart';
import '../../../utils/web_responsive_utils.dart';
import 'dentist_homescreen.dart';
import 'dentist_profilescreen.dart';
import 'dentist_reports_screen.dart';

class DentistOrdersScreen extends StatefulWidget {

  int? initialTab;

   DentistOrdersScreen({super.key,this.initialTab});

  @override
  State<DentistOrdersScreen> createState() => _DentistOrdersScreenState();
}

class _DentistOrdersScreenState extends State<DentistOrdersScreen> with TickerProviderStateMixin {
  final DoctorServiceController doctorServiceController =Get.find();
  final DentistOrderController dentistOrderController =Get.find();

  String selectedOrderType = "processing";
  late TabController _tabController;
  List<String> tabTitles = ['Orders', 'Closed Orders', 'Cancelled Orders'];

  String appbarTitle = "Orders";
  String searchQuery = ""; // Stores the current search input
  List<OrdersModel> allOrders = []; // Stores all orders fetched from API
  List<OrdersModel> filteredOrders = []; // Stores orders matching the search

  void filterOrders(String query) {
    if (allOrders.isEmpty) {
      log("⚠️ No orders available for filtering.");
      return;
    }

    log("🔎 Searching for: $query");

    String searchQuery = query.toLowerCase().trim();
    List<String> queryWords = searchQuery.split(" ");

    List<OrdersModel> relevantOrders = [];

    // Filter by tab
    if (_tabController.index == 0) {
      relevantOrders = allOrders.where((order) => order.orderStatus == "processing").toList();
    } else if (_tabController.index == 1) {
      relevantOrders = allOrders.where((order) => order.orderStatus == "completed").toList();
    } else {
      relevantOrders = allOrders.where((order) => order.orderStatus == "cancelled").toList();
    }

    log("✅ Orders matching current tab: ${relevantOrders.length}");

    // Apply search filter
    if (searchQuery.isNotEmpty) {
      relevantOrders = relevantOrders.where((order) {
        String orderName = order.patientName.toLowerCase();
        String orderId = order.orderId.toLowerCase();
        String organizationName = order.toOrg.name.toLowerCase();
        String formattedDate = order.orderDate != null
            ? DateFormat('dd-MM-yyyy').format(order.orderDate).toLowerCase()
            : '';

        List<String> serviceNames = order.orderServices
            .map((service) => service.orgservice?.servicess.servicename.toLowerCase() ?? '')
            .toList();

        bool matches = queryWords.every((word) =>
        orderName.contains(word) ||
            orderId.contains(word) ||
            organizationName.contains(word) ||
            formattedDate.contains(word) ||
            serviceNames.any((serviceName) => serviceName.contains(word))
        );

        if (matches) {
          log("✅ Matched Order: '${order.patientName}' (${order.orderId}) on $formattedDate");
        }

        return matches;
      }).toList();
    }

    setState(() {
      filteredOrders = relevantOrders;
    });

    log("🎯 Filtered Orders Count: ${filteredOrders.length}");
  }




  // @override
  // void initState() {
  //   super.initState();
  //   _tabController = TabController(length: tabTitles.length, vsync: this,initialIndex: widget.initialTab!);
  //
  //   _tabController.addListener(() {
  //     setState(() {
  //       appbarTitle = tabTitles[_tabController.index];
  //     });
  //
  //     fetchOrdersForCurrentTab();
  //   });
  //
  //   WidgetsBinding.instance.addPostFrameCallback((_) {
  //     fetchOrdersForCurrentTab(); // Fetch default orders on screen load
  //   });
  // }


  @override
  void initState() {
    super.initState();

    _tabController = TabController(
      length: tabTitles.length,
      vsync: this,
      initialIndex: widget.initialTab ?? 0,
    );

    _tabController.addListener(() {
      setState(() {
        appbarTitle = tabTitles[_tabController.index];
      });
      fetchOrdersForCurrentTab();
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        _tabController.index = widget.initialTab ?? 0;
        appbarTitle = tabTitles[_tabController.index];
      });
      fetchOrdersForCurrentTab();
    });

  }




  /// Helper function to update order lists
  void updateOrders() {
    final controller = Get.find<DentistOrderController>();

    setState(() {
      allOrders = List.from(controller.orderList); // Store full order list
      filteredOrders = List.from(allOrders); // Initially, show all orders
    });

    log("All Orders Count: ${allOrders.length}");
  }

  void fetchOrdersForCurrentTab() async {
    final controller = Get.find<DentistOrderController>();

    String status;
    if (_tabController.index == 0) {
      status = 'processing'; // Active orders
    } else if (_tabController.index == 1) {
      status = 'completed'; // Closed orders
    } else {
      status = 'cancelled'; // Cancelled orders
    }

    await controller.fetchStatus(status: status); // Fetch data from API

    // log API results
    log("API Fetched Orders Count: ${controller.orderList.length}");
    for (var order in controller.orderList) {
    }

    setState(() {
      allOrders = List.from(controller.orderList);
      filteredOrders = List.from(allOrders); // Ensure initial display is correct
    });

    log("Stored Orders Count in allOrders: ${allOrders.length}");
  }


  @override
  void dispose() {
    _tabController.dispose();

    super.dispose();
    widget.initialTab;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: kIsWeb
          ? WebResponsiveUtils.dentistWebAppBar(1, context)
          : CustomAppBar(
              leading: IconButton(
                onPressed: () {
                  // Get.offAll(() => const DentistHomeScreen());
                  Get.back();
                },
                icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
              ),
              title: appbarTitle,
              backgroundColor: AppColors.primary,
              textColor: Colors.white,
              centerTitle: true,
            ),
      drawer: (MediaQuery.of(context).size.width <= 600 && kIsWeb)
          ? Drawer(
        child: ListView(
          children: [
            DrawerHeader(
              decoration: const BoxDecoration(color: AppColors.primary),
              child: CustomImageView(
                fit: BoxFit.none,
                imagePath: AppIcons.appLogo,
                color: AppColors.white,
              ),
            ),
            ListTile(
              leading: CustomImageView(
                imagePath: AppIcons.home,
              ),
              title: const Text('Home'),
              onTap: () {
                Get.offAll(() => const DentistHomeScreen());
              },
            ),
            ListTile(
              //leading: Icon(AppIcons.orders),
              leading: CustomImageView(
                imagePath: AppIcons.orders,
              ),
              title: const Text('Order'),
              onTap: () {
                Get.offAll(() =>  DentistOrdersScreen());
              },
            ),
            ListTile(
              //leading: Icon(AppIcons.orders),
              leading: CustomImageView(
                imagePath: AppIcons.reports,
              ),
              title: const Text('Reports'),
              onTap: () {
                Get.offAll(() =>  const DentistReportsScreen());
              },
            ),
            ListTile(
              //leading: Icon(AppIcons.orders),
              leading: CustomImageView(
                imagePath: AppIcons.profile,
              ),
              title: const Text('Profile'),
              onTap: () {
                Get.offAll(() => const DentistProfilescreen());
              },
            ),

          ],
        ),
      )
          : null,
      body: SafeArea(
        child: Center(
          child: Column(
            children: [
              const SizedBox(
                height: AppSizes.defaultSpace,
              ),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                height: 48,
                width: MediaQuery.of(context).size.width > 600 ? Get.size.width*0.5 : double.infinity,
                decoration: BoxDecoration(
                  color: AppColors.background1,
                  borderRadius: BorderRadius.circular(AppSizes.borderRadiusSm), // Optional
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withOpacity(0.16),
                      offset: const Offset(0, 2),
                      blurRadius: 4,
                    ),
                  ],
                ),

                child: Center(
                  child:
                  CustomSearchField<String>(
                    fetchItems: (query) async {
                      filterOrders(query);
                      return []; // ✅ Hide dropdown by returning an empty list
                    },
                    hintText: "Search",
                    itemAsString: (item) => item,
                    onChanged: (query) {
                      setState(() {
                        searchQuery = query;
                      });
                      filterOrders(query);
                    },
                  )

                ),
              ),

              const SizedBox(height: AppSizes.spaceBtwItems,),

              CustomElevatedButton(
                text: 'Create Order',

                  buttonTextStyle: const TextStyle(fontSize: 16,color: Colors.white),
                margin: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                alignment: Alignment.topRight,
                height: 36,
                width: 130,
                buttonStyle: ButtonStyle(
                    backgroundColor: MaterialStateProperty.all(AppColors.primary),

                  shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppSizes.borderRadiusSm)
                    )),
                    padding: const WidgetStatePropertyAll(EdgeInsets.only(right:10))
                ),
                onPressed: (){
                  Get.to( () => const CreateOrderSelectionScreen(),
                  arguments: {

                      "services" : doctorServiceController.service,

                  });
                },
              ),


              const SizedBox(height: AppSizes.spaceSmall,),

              Container(
                // margin: const EdgeInsets.only( right: AppSizes.md),
                color: Colors.white,
                child: SizedBox(
                  height: 35,
                  child: TabBar(
                    controller: _tabController,
                    unselectedLabelColor: AppColors.black,
                    indicatorColor: AppColors.primary,
                    labelColor: AppColors.black,
                    labelStyle: CustomTextStyles.b4_1,
                    padding: EdgeInsets.zero,
                    labelPadding: EdgeInsets.zero,
                    isScrollable: false,
                    indicatorPadding: EdgeInsets.zero,
                    onTap: (index) {
                      final controller = Get.find<DentistOrderController>();

                      if (index == 1) {
                        controller.fetchStatus(status: "completed");
                      } else if (index == 2) {
                        controller.fetchStatus(status: "cancelled");
                      }
                    },

                    tabs: const [
                      Padding(
                        padding:  EdgeInsets.symmetric(horizontal: 4.0),
                        child: Tab(
                          text: "Open Orders",
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 4.0) ,
                        child: Tab(
                          text: "Closed Orders",
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 4.0) ,
                        child: Tab(
                          text: "Cancelled Orders",
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Expanded(
                child:SingleChildScrollView(
                  child: SizedBox(
                        height: MediaQuery.of(context).size.height * 0.8,
                        child: TabBarView(
                          controller: _tabController,
                          children: [
                            activeWidget(),
                            closedWidget(),
                            cancelledWidget()
                          ],
                        ),
                      ),
                ),
              ),
            ],
          ),
        ),
      ),

    );
  }


  // Widget activeWidget() {
  //   return RefreshIndicator(
  //     onRefresh: () async {
  //       await dentistOrderController.fetchStatus(status: 'processing'); // ✅ Refresh orders
  //     },
  //     child: Column(
  //       children: [
  //         Expanded(
  //           child: GetBuilder<DentistOrderController>(
  //             builder: (controller) {
  //               // ✅ Show filteredOrders if searching, otherwise show all orders
  //               List<OrdersModel> displayOrders = searchQuery.isNotEmpty ? filteredOrders : controller.orderList;
  //
  //               if (controller.isLoading) {
  //                 return const Center(child: CircularProgressIndicator());
  //               }
  //
  //               if (displayOrders.isEmpty) {
  //                 return const Center(child: Text("No orders available."));
  //               }
  //
  //               return GridView.builder(
  //                 shrinkWrap: true,
  //                 //physics: const NeverScrollableScrollPhysics(),
  //                 gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
  //                   crossAxisCount: WebResponsiveUtils.responsiveGridItemCount(context),
  //                   mainAxisExtent: 100,
  //                 ),
  //                 itemCount: displayOrders.length,
  //                 itemBuilder: (BuildContext context, int index) {
  //                   final order = displayOrders[index];
  //
  //                   return Padding(
  //                     padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
  //                     child: GestureDetector(
  //                       onTap: () {
  //                         Get.to(DentistOrderDetailsScreen(orderId: order.id));
  //                       },
  //                       child: Container(
  //                         margin: const EdgeInsets.only(top: AppSizes.md),
  //                         padding: const EdgeInsets.symmetric(vertical: AppSizes.sm, horizontal: AppSizes.sm),
  //                         height: 76,
  //                         decoration: BoxDecoration(
  //                           color: Colors.white,
  //                           borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
  //                           boxShadow: [
  //                             BoxShadow(color: AppColors.black.withOpacity(0.16), blurRadius: 2)
  //                           ],
  //                         ),
  //                         child: Row(
  //
  //                           children: [
  //                             CustomImageView(
  //                               imagePath: (order.toOrg.file1 != null && order.toOrg.file1.isNotEmpty)
  //                                   ? order.toOrg.file1
  //                                   : AppImages.test, // Replace with your test image path
  //                               width: 60,
  //                               height: 60,
  //                             ),
  //                             const SizedBox(width: AppSizes.sm),
  //                             Expanded(
  //                               child: Column(
  //                                 crossAxisAlignment: CrossAxisAlignment.start,
  //                                 children: [
  //                                   Text(order.toOrg.name,
  //                                       style: CustomTextStyles.b2_1.copyWith(fontWeight: FontWeight.w600)),
  //                                   const SizedBox(height: AppSizes.spaceExtraSmall),
  //                                   Flexible(
  //                                     child: Text(
  //                                       "Services: ${order.orderServices!
  //                                           .map((e) => e.orgservice?.servicess?.servicename ?? 'Unknown')
  //                                           .where((name) => name != 'Unknown')
  //                                           .take(2)
  //                                           .join(', ') + (order.orderServices!.length > 3 ? '...' : '')}",
  //                                       maxLines: 1,
  //                                       overflow: TextOverflow.ellipsis,
  //                                       style: CustomTextStyles.b6_3,
  //                                     ),
  //                                   ),
  //
  //
  //                                   const SizedBox(height: AppSizes.spaceExtraSmall),
  //                                   Row(
  //                                     children: [
  //                                       CustomImageView(
  //                                         imagePath: AppIcons.location,
  //                                         width: 14,
  //                                         height: 14,
  //                                       ),
  //                                       const SizedBox(width: AppSizes.spaceExtraSmall),
  //                                       Text(
  //                                         (order.toOrg.address as List<dynamic>).join(", ").length > 30
  //                                             ? "${(order.toOrg.address as List<dynamic>).join(", ").substring(0, 30)}..."
  //                                             : (order.toOrg.address as List<dynamic>).join(", ") ?? "No address",
  //                                         style: CustomTextStyles.b6_3,
  //                                         maxLines: 1,
  //                                         overflow: TextOverflow.ellipsis,
  //                                       ),
  //                                     ],
  //                                   ),
  //
  //                                 ],
  //                               ),
  //                             ),
  //                           ],
  //                         ),
  //                       ),
  //                     ),
  //                   );
  //                 },
  //               );
  //             },
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  Widget activeWidget() {
    return RefreshIndicator(
      onRefresh: () async {
        await dentistOrderController.fetchStatus(status: 'processing'); // ✅ Refresh orders
      },
      child: Column(
        children: [
          Expanded(
            child: GetBuilder<DentistOrderController>(
              builder: (controller) {
                List<OrdersModel> displayOrders =
                searchQuery.isNotEmpty ? filteredOrders : controller.orderList;

                if (controller.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (displayOrders.isEmpty) {
                  return const Center(child: Text("No orders available."));
                }

                return GridView.builder(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: WebResponsiveUtils.responsiveGridItemCount(Get.context!),
                    mainAxisExtent: 120,
                  ),
                  padding: const EdgeInsets.symmetric(vertical: AppSizes.md).copyWith(bottom: 100),
                  itemCount: displayOrders.length,
                  itemBuilder: (BuildContext context, int index) {
                    final order = displayOrders[index];

                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: AppSizes.md, vertical: 6),
                      child: GestureDetector(
                        onTap: () {
                          Get.to(DentistOrderDetailsScreen(orderId: order.id));
                        },
                        child: Container(
                          height: 100,
                          padding: const EdgeInsets.symmetric(vertical: AppSizes.sm, horizontal: AppSizes.sm),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                            boxShadow: [
                              BoxShadow(color: AppColors.black.withOpacity(0.16), blurRadius: 2)
                            ],
                          ),
                          child: Row(
                            children: [
                              CustomImageView(
                                imagePath: (order.toOrg.file1.isNotEmpty)
                                    ? order.toOrg.file1
                                    : AppImages.test, // Replace with your test image path
                                width: 80,
                                height: 80,
                                radius: BorderRadius.circular(8),
                              ),
                              const SizedBox(width: AppSizes.sm),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const SizedBox(height: AppSizes.sm2),
                                    Text(
                                      order.toOrg.name,
                                      style: CustomTextStyles.b2_1.copyWith(fontWeight: FontWeight.w600),
                                    ),
                                    const SizedBox(height: AppSizes.spaceExtraSmall),
                                    Text(
                                      "Services: ${order.orderServices
                                          .map((e) => e.orgservice?.servicess.servicename ?? 'Unknown')
                                          .where((name) => name != 'Unknown')
                                          .take(2)
                                          .join(', ') + (order.orderServices.length > 3 ? '...' : '')}",
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: CustomTextStyles.b6_3,
                                    ),
                                    const SizedBox(height: AppSizes.spaceExtraSmall),
                                    Row(
                                      children: [
                                        CustomImageView(
                                          imagePath: AppIcons.location,
                                          width: 14,
                                          height: 14,
                                        ),
                                        const SizedBox(width: AppSizes.spaceExtraSmall),
                                        // Expanded(
                                        //   child: Text(
                                        //     (order.address)!.join(", ").length > 30
                                        //         ? "${(order.toOrg.address).join(", ").substring(0, 30)}..."
                                        //         : (order.toOrg.address).join(", ") ,
                                        //     style: CustomTextStyles.b6_3,
                                        //     maxLines: 1,
                                        //     overflow: TextOverflow.ellipsis,
                                        //   ),
                                        // ),
                                        Expanded(
                                          child: Text(order.address??"",
                                            style: CustomTextStyles.b6_3,
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
          const SizedBox(height: 80,)
        ],
      ),
    );
  }


  Widget closedWidget() {
    return RefreshIndicator(
      onRefresh: () async {
        await dentistOrderController.fetchStatus(status: 'completed');
      },
      child: Column(
        children: [
          const SizedBox(height: 10),
          GetBuilder<DentistOrderController>(
            builder: (controller) {
              if (controller.orderList.isNotEmpty) {
                return Align(
                  alignment: Alignment.centerRight,
                  child: GestureDetector(
                    onTap: () async {
                      MediaQuery.of(context).size.width > 600 ? clearAllCompletedWeb(context) : await clearSheet1();
                    },
                    child: Padding(
                      padding: const EdgeInsets.only(right: AppSizes.spaceBtwItems, bottom: AppSizes.spaceSmall),
                      child: Text("Clear All", style: CustomTextStyles.b3_primary),
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
          Expanded(
            child: GetBuilder<DentistOrderController>(

              builder: (controller) {
                List<OrdersModel> displayOrders = searchQuery.isNotEmpty ? filteredOrders : controller.orderList;
            
                if (controller.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }
            
                if (displayOrders.isEmpty) {
                  return const Center(child: Text("No orders available."));
                }
            
                return GridView.builder(
                  padding: EdgeInsets.only(bottom: 100),
                  //shrinkWrap: true,
                 // physics: NeverScrollableScrollPhysics(), // ✅ Prevents nested scrolling issues
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: WebResponsiveUtils.responsiveGridItemCount(Get.context!),
                    mainAxisExtent: 152,
                  ),
                  itemCount: displayOrders.length,
                  itemBuilder: (BuildContext context, int index) {
                    final order = displayOrders[index];
            
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                      child: GestureDetector(
                        onTap: () {
                          Get.to(DentistOrderDetailsScreen(orderId: order.id));
                        },
                        child: Container(
                          margin: const EdgeInsets.only(top: AppSizes.md),
                          padding: const EdgeInsets.symmetric(vertical: AppSizes.sm, horizontal: AppSizes.sm),
                          // height: 110,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                            boxShadow: [BoxShadow(color: AppColors.black.withOpacity(0.16), blurRadius: 2)],
                          ),
                          child: Row(
                            children: [
                              CustomImageView(
                                imagePath: (order.toOrg.file1.isNotEmpty)
                                    ? order.toOrg.file1
                                    : AppImages.test,
                                radius: BorderRadiusStyle.radius8,
                                width: 70,
                                height: 80,
                              ),
                              const SizedBox(width: AppSizes.sm),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(order.toOrg.name,
                                        style: CustomTextStyles.b2_1.copyWith(fontWeight: FontWeight.w600)),
                                    const SizedBox(height: AppSizes.spaceExtraSmall),
                                    Text(
                                      "Services: ${order.orderServices
                                          .map((e) => e.orgservice?.servicess.servicename ?? 'Unknown')
                                          .where((service) => service.isNotEmpty)
                                          .join(', ')}",
                                      maxLines: 1,
                                      style: CustomTextStyles.b6_3.copyWith(overflow: TextOverflow.ellipsis),
                                    ),
                                    const SizedBox(height: AppSizes.spaceExtraSmall),
                                    Row(
                                      children: [
                                        CustomImageView(
                                          imagePath: AppIcons.location,
                                          width: 14,
                                          height: 14,
                                        ),
                                        const SizedBox(width: AppSizes.spaceExtraSmall),
                                        Text(
                                          (order.toOrg.address).join(", ").length > 40
                                              ? "${(order.toOrg.address).join(", ").substring(0, 40)}..."
                                              : (order.toOrg.address).join(", "),
                                          style: CustomTextStyles.b6_3,
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: AppSizes.spaceExtraSmall),
                                    Row(
                                      children: [
                                        Text("Order Status: ", style: CustomTextStyles.b5.copyWith(color: AppColors.darkGrey)),
                                        Text(
                                            "${order.orderStatus[0].toUpperCase()}${order.orderStatus.substring(1)}",

                                            style: CustomTextStyles.b5.copyWith(color: Colors.green)),
                                      ],
                                    ),
                                    Row(
                                      mainAxisAlignment: order.paymentStatus == 'processing'
                                          ? MainAxisAlignment.start
                                          : MainAxisAlignment.start,
                                      children: [
                                        if (order.paymentStatus != 'processing')
                                          Row(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text("Payment: ", style: CustomTextStyles.b5.copyWith(color: AppColors.darkGrey)),
                                              Text(
                                                  "${order.paymentStatus[0].toUpperCase()}${order.paymentStatus.substring(1)}",
                                                  style: CustomTextStyles.b5.copyWith(color: Colors.green)),
            
                                            ],
                                          ),
            
                                        if (order.paymentStatus == 'processing')
                                          Padding(
                                            padding: const EdgeInsets.only(top: 4.0),
                                            child: CustomElevatedButton(
                                              onPressed: () {
                                                Get.to(() => DentistOrderDetailsScreen(orderId: order.id));
                                              },
                                              text: "Pay now",
                                              buttonTextStyle: CustomTextStyles.b4.copyWith(color: Colors.white),
                                              height: 32,
                                              width: (MediaQuery.of(context).size.width < 600 ? Get.size.width : (MediaQuery.of(context).size.width > 600 && MediaQuery.of(context).size.width < 1000) ?  Get.size.width * 0.4  :  Get.size.width * 0.5)-120,
                                              buttonStyle: ButtonStyle(
                                                shape: WidgetStatePropertyAll(
                                                  RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                                                ),
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),

                                    if(order.paymentStatus=="unpaid" && order.toOrg.organizationType.organizationType != "Radiology")
                                      const Text("(Invoice has not been raised)",maxLines: 2,overflow: TextOverflow.ellipsis,style: TextStyle(color: Colors.grey),),

                                    // if(order.paymentStatus=="unpaid" )
                                    //   const Text("(Invoice has not been raised)",maxLines: 2,overflow: TextOverflow.ellipsis,style: TextStyle(color: Colors.grey),),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
          const SizedBox(height: 100,)
        ],
      ),
    );
  }


  Widget cancelledWidget() {
    return RefreshIndicator(
      onRefresh: () async {
        await dentistOrderController.fetchStatus(status: 'cancelled'); // ✅ Refresh cancelled orders
      },
      child: Column(
        children: [
          const SizedBox(height: 10,),
          GetBuilder<DentistOrderController>(
            builder: (controller){
              if(controller.orderList.isNotEmpty) {
                return Align(
                  alignment: Alignment.centerRight,
                  child: GestureDetector(
                    onTap: () async{
                      MediaQuery.of(context).size.width > 600 ? clearAllCancelledWeb(context) : await clearSheet();
                    },
                    child: Padding(
                      padding: const EdgeInsets.only(right: AppSizes.spaceBtwItems,bottom: AppSizes.spaceSmall),
                      child: Text(
                        "Clear All",
                        style: CustomTextStyles.b3_primary,
                      ),
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
          Expanded(
            child: GetBuilder<DentistOrderController>(
              builder: (controller) {
                // ✅ Show filteredOrders if searching, otherwise show all orders
                List<OrdersModel> displayOrders =
                searchQuery.isNotEmpty ? filteredOrders : controller.orderList;

                if (controller.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (displayOrders.isEmpty) {
                  return const Center(child: Text("No cancelled orders available."));
                }

                return GridView.builder(
                  padding: const EdgeInsets.only(bottom: 100),
                  shrinkWrap: true,
                  //physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: WebResponsiveUtils.responsiveGridItemCount(context),
                    mainAxisExtent: 130,
                  ),
                  itemCount: displayOrders.length,
                  itemBuilder: (BuildContext context, int index) {
                    final order = displayOrders[index];

                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                      child: GestureDetector(
                        onTap: () {
                          Get.to(DentistOrderDetailsScreen(orderId: order.id));
                        },
                        child: Container(
                          margin: const EdgeInsets.only(top: AppSizes.md),
                          padding: const EdgeInsets.symmetric(vertical: AppSizes.sm, horizontal: AppSizes.sm),
                          //height: 76,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                            boxShadow: [
                              BoxShadow(color: AppColors.black.withOpacity(0.16), blurRadius: 2)
                            ],
                          ),
                          child: Row(
                            children: [
                              CustomImageView(
                                imagePath: order.toOrg.file1,
                                radius: BorderRadiusStyle.radius8,

                                width: 80,
                                height: 80,
                              ),
                              const SizedBox(width: AppSizes.sm),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(order.toOrg.name,
                                        style: CustomTextStyles.b2_1.copyWith(fontWeight: FontWeight.w600)),
                                    const SizedBox(height: AppSizes.spaceExtraSmall),
                                    Text(
                                      "Services: ${order.orderServices
                                          .where((e) => e.orgservice != null)
                                          .map((e) => e.orgservice!.servicess.servicename)
                                          .join(', ')}",
                                      maxLines: 1,
                                      style: CustomTextStyles.b6_3.copyWith(overflow: TextOverflow.ellipsis),
                                    ),


                                    const SizedBox(height: AppSizes.spaceExtraSmall),
                                    Row(
                                      children: [
                                        CustomImageView(
                                          imagePath: AppIcons.location,
                                          width: 14,
                                          height: 14,
                                        ),
                                        const SizedBox(width: AppSizes.spaceExtraSmall),
                                        Text(
                                          (order.toOrg.address).join(", ").length > 30
                                              ? "${(order.toOrg.address).join(", ").substring(0, 30)}..."
                                              : (order.toOrg.address).join(", "),
                                          style: CustomTextStyles.b6_3,
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: AppSizes.spaceExtraSmall),
                                    Row(
                                      children: [
                                        const Text("Order Status:",style: TextStyle(fontSize: 14,color: AppColors.grey1,fontWeight: FontWeight.w400),),

                                        const SizedBox(width: AppSizes.spaceExtraSmall),
                                        Text(
                                            "${order.orderStatus[0].toUpperCase()}${order.orderStatus.substring(1)}",
                                            style: const TextStyle(fontSize: 14,color: AppColors.red,fontWeight: FontWeight.w400)),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
          const SizedBox(height: 100,)
        ],
      ),
    );
  }



  Future clearSheet() {
    return Get.bottomSheet(
      Container(
        height: 262,
        width: MediaQuery.of(context).size.width > 600 ? Get.size.width* 0.8 : Get.size.width,
        decoration: const BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(30),
            topRight: Radius.circular(30),
          ),
        ),
        child: Column(
          children: [
            const SizedBox(
              height: AppSizes.defaultSpace,
            ),
            Text(
              "Clear All",
              style: CustomTextStyles.h6.copyWith(color: Colors.black),
            ),
            const SizedBox(
              height: 6,
            ),
            Container(
              height: 1,
              width: 110,
              color: Colors.black,
            ),
            const SizedBox(
              height: AppSizes.defaultSpace,
            ),
            Text(
              "Are you sure you want to",
              style: CustomTextStyles.b2_1,

            ),
            Center(
              child: Text(
                "Clear all ?",
                style: CustomTextStyles.b2_1,

              ),
            ),
            const SizedBox(
              height: AppSizes.defaultSpace,
            ),
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () {
                      Get.back();
                    },
                    child: Container(
                      height: 56,
                      margin: const EdgeInsets.symmetric(horizontal:AppSizes.md),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: AppColors.primary2,
                        borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                      ),
                      child: Text(
                        "Cancel",
                        style: CustomTextStyles.b3_primary,

                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: InkWell(
                    onTap: ()  {
                     dentistOrderController.clearAllOrders("cancelled");
                      Get.back();
                    },
                    child: Container(
                      height: 56,
                      margin: const EdgeInsets.only(right: AppSizes.md),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                      ),
                      child: Text(
                        "Clear",
                        style: CustomTextStyles.b3_1,

                      ),
                    ),
                  ),
                )
              ],
            )
          ],
        ),
      ),
    );
  }

  Future clearSheet1() {
    return Get.bottomSheet(
      Container(
        height: 262,
        width: MediaQuery.of(context).size.width > 600 ? Get.size.width* 0.8 : Get.size.width,
        decoration: const BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(30),
            topRight: Radius.circular(30),
          ),
        ),
        child: Column(
          children: [
            const SizedBox(
              height: AppSizes.defaultSpace,
            ),
            Text(
              "Clear All",
              style: CustomTextStyles.h6.copyWith(color: Colors.black),
            ),
            const SizedBox(
              height: 6,
            ),
            Container(
              height: 1,
              width: 110,
              color: Colors.black,
            ),
            const SizedBox(
              height: AppSizes.defaultSpace,
            ),
            Text(
              "Are you sure you want to",
              style: CustomTextStyles.b2_1,

            ),
            Center(
              child: Text(
                "Clear all ?",
                style: CustomTextStyles.b2_1,

              ),
            ),
            const SizedBox(
              height: AppSizes.defaultSpace,
            ),
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () {
                      Get.back();
                    },
                    child: Container(
                      height: 56,
                      margin: const EdgeInsets.symmetric(horizontal:AppSizes.md),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: AppColors.primary2,
                        borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                      ),
                      child: Text(
                        "Cancel",
                        style: CustomTextStyles.b3_primary,

                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: InkWell(
                    onTap: ()  {
                      dentistOrderController.clearAllOrders("completed");

                      Get.back();
                    },
                    child: Container(
                      height: 56,
                      margin: const EdgeInsets.only(right: AppSizes.md),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                      ),
                      child: Text(
                        "Clear",
                        style: CustomTextStyles.b3_1,

                      ),
                    ),
                  ),
                )
              ],
            )
          ],
        ),
      ),
    );
  }

  Future clearAllCancelledWeb(BuildContext context) {
    return showDialog(
      context: context,
      barrierDismissible: true, // User must tap button
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30),
          ),
          child: Container(
            // height: 280, // Slightly more than 262 for better spacing
            width: MediaQuery.of(context).size.width * 0.4, // Not full screen
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: AppSizes.defaultSpace),
                Text(
                  "Clear All",
                  style: CustomTextStyles.h6,
                ),
                const SizedBox(height: AppSizes.spaceBtwList),
                Container(
                  width: 100,
                  height: 1.5,
                  color: Colors.black,),
                const SizedBox(height: AppSizes.defaultSpace),
                Text(
                  "Are you sure you want to Clear all ?",
                  textAlign: TextAlign.center,
                  style: CustomTextStyles.b2_1,
                ),
                const SizedBox(height: AppSizes.defaultSpace),
                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          Navigator.of(context).pop(); // Close the dialog
                        },
                        child: Container(
                          height: 56,
                          margin: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: AppColors.primary2,
                            borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                          ),
                          child: Text(
                            "Cancel",
                            style: CustomTextStyles.b3_primary,
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child:InkWell(
                        onTap: () async {
                          dentistOrderController.clearAllOrders("cancelled");
                          Get.back();                    },
                        child: Container(
                          height: 56,
                          margin: const EdgeInsets.only(right: AppSizes.md),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                          ),
                          child: Text(
                            "Clear",
                            style: CustomTextStyles.b3_1,
                          ),
                        ),
                      ),
                    )
                  ],
                ),
                const SizedBox(height: AppSizes.defaultSpace),
              ],
            ),
          ),
        );
      },
    );
  }

  Future clearAllCompletedWeb(BuildContext context) {
    return showDialog(
      context: context,
      barrierDismissible: true, // User must tap button
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30),
          ),
          child: Container(
            // height: 280, // Slightly more than 262 for better spacing
            width: MediaQuery.of(context).size.width * 0.4, // Not full screen
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: AppSizes.defaultSpace),
                Text(
                  "Clear All",
                  style: CustomTextStyles.h6,
                ),
                const SizedBox(height: AppSizes.spaceBtwList),
                Container(
                  width: 100,
                  height: 1.5,
                  color: Colors.black,),
                const SizedBox(height: AppSizes.defaultSpace),
                Text(
                  "Are you sure you want to Clear all ?",
                  textAlign: TextAlign.center,
                  style: CustomTextStyles.b2_1,
                ),
                const SizedBox(height: AppSizes.defaultSpace),
                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          Navigator.of(context).pop(); // Close the dialog
                        },
                        child: Container(
                          height: 56,
                          margin: const EdgeInsets.symmetric(horizontal: AppSizes.md),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: AppColors.primary2,
                            borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                          ),
                          child: Text(
                            "Cancel",
                            style: CustomTextStyles.b3_primary,
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child:InkWell(
                        onTap: () async {
                          dentistOrderController.clearAllOrders("completed");

                          Get.back();
                        },
                        child: Container(
                          height: 56,
                          margin: const EdgeInsets.only(right: AppSizes.md),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
                          ),
                          child: Text(
                            "Clear",
                            style: CustomTextStyles.b3_1,
                          ),
                        ),
                      ),
                    )
                  ],
                ),
                const SizedBox(height: AppSizes.defaultSpace),
              ],
            ),
          ),
        );
      },
    );
  }



}
